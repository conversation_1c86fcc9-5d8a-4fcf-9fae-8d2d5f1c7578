# services/user - 技术概念验证 (POC) 开发指南

基于 docs/PROJECT_BLUEPRINT.md（版本 1.1）已确定技术栈：
FastAPI, SQLModel, Pydantic 2.5+, Uvicorn, PostgreSQL 13+, Redis 6+, Dramatiq, Manticore Search 6.0+, Docker Compose, Traefik。

**作者**: Roo
**状态**: ✅ **已完成实现**

---
## 目的与范围（按蓝图）

User Service 提供用户注册、登录、会话管理与基础权限控制（JWT）。本 POC 验证与 PROJECT_BLUEPRINT 中规定的认证/鉴权方案兼容，并能为其他服务（BFF/Gateway、Topic、Document 等）提供统一的鉴权接口。

---
## 技术细节（与蓝图一致）

- 后端：FastAPI（异步）
- 数据模型与 ORM：SQLModel（与 Pydantic 2.5+）
- 密码与安全：bcrypt（PassLib），JWT（pyjwt或jose）
- 驱动：asyncpg（PostgreSQL）
- 会话/缓存：Redis（用于短期会话缓存、速率限制等）
- 异步任务：Dramatiq（用于邮件、通知等异步工作）
- 配置：参照 PROJECT_BLUEPRINT 的配置管理策略（.env 在开发，生产通过平台注入）

---
## 📁 POC 文件结构

```
demo/user_poc/
├── README.md              # 本文件
├── requirements.txt       # Python 依赖
├── main.py               # 主要 POC 实现
├── test_poc.py           # 自动化测试脚本
├── run_poc.py            # 运行和管理脚本
├── docker-compose.yml    # Docker 容器编排
├── Dockerfile           # Docker 镜像构建
└── .env.example         # 环境变量示例
```

---
## 🚀 快速开始

### 方法一：直接运行（推荐）

1. **安装依赖**：
```bash
cd demo/user_poc
pip install -r requirements.txt
```

2. **启动服务并运行测试**：
```bash
python run_poc.py test
```

3. **交互式测试**：
```bash
python run_poc.py interactive
```

### 方法二：手动运行

1. **启动服务**：
```bash
python main.py
```

2. **运行测试**（新终端）：
```bash
python test_poc.py
```

### 方法三：Docker 运行

```bash
docker-compose up --build
```

---
## 🔌 API 接口

### 已实现的 API 端点

| 方法 | 路径 | 描述 | 认证 |
|------|------|------|------|
| POST | `/api/v1/auth/register` | 用户注册 | 无 |
| POST | `/api/v1/auth/login` | 用户登录 | 无 |
| GET | `/api/v1/me` | 获取当前用户信息 | Bearer Token |
| POST | `/api/v1/auth/refresh` | 刷新访问令牌 | Refresh Token |
| GET | `/health` | 健康检查 | 无 |
| GET | `/` | 服务信息 | 无 |

### API 使用示例

#### 1. 用户注册
```bash
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword123",
    "full_name": "Test User"
  }'
```

#### 2. 用户登录
```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword123"
  }'
```

#### 3. 访问受保护路由
```bash
curl -X GET "http://localhost:8000/api/v1/me" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### 4. 刷新令牌
```bash
curl -X POST "http://localhost:8000/api/v1/auth/refresh?refresh_token=YOUR_REFRESH_TOKEN"
```

---
## 集成点（按蓝图）

- 上游：前端（React）
- 下游：所有需鉴权的服务（BFF/Gateway, Topic, Document, Conversation）
- 配置（.env）：
```
JWT_SECRET=supersecret
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
DATABASE_URL=postgresql+asyncpg://user:pass@postgres:5432/db
REDIS_URL=redis://redis:6379/0
```

---
## ✅ 验证清单（与蓝图 P0 对齐）

- [x] 用户注册成功并将哈希密码存储（本 POC 使用内存存储，生产环境使用 PostgreSQL）
- [x] 登录返回有效 JWT，受保护路由能正确验证
- [x] 刷新/撤销策略能在简单测试中生效
- [x] 与 Gateway 的鉴权流程兼容（标准 Bearer Token 认证）

---
## 🧪 测试结果

运行 `python run_poc.py test` 将执行以下测试：

1. ✅ 健康检查
2. ✅ 用户注册
3. ✅ 重复注册拒绝
4. ✅ 用户登录
5. ✅ 无效登录拒绝
6. ✅ 受保护路由访问
7. ✅ 未授权访问拒绝
8. ✅ 令牌刷新
9. ✅ 刷新后令牌访问

---
## 🔧 配置说明

### 环境变量

复制 `.env.example` 为 `.env` 并根据需要修改：

```bash
cp .env.example .env
```

主要配置项：
- `JWT_SECRET`: JWT 签名密钥（生产环境必须更改）
- `ACCESS_TOKEN_EXPIRE_MINUTES`: 访问令牌过期时间
- `DATABASE_URL`: 数据库连接（可选，默认使用内存存储）
- `REDIS_URL`: Redis 连接（可选，默认使用内存存储）

---
## 🚨 注意事项

### 当前 POC 限制
- **内存存储**: 服务重启后数据丢失，仅用于演示
- **简化安全**: 生产环境需要更严格的安全策略
- **无邮件验证**: 密码重置等功能需要在生产版本中实现

### 生产环境建议
- 使用 PostgreSQL 持久化存储
- 使用 Redis 进行会话管理
- 实施密码复杂度策略
- 添加速率限制和防暴力破解
- 实现邮件验证和密码重置

---
## 📚 参考资料

- [backend/app/core/security.py](../../backend/app/core/security.py) - 项目已有安全逻辑
- [docs/PROJECT_BLUEPRINT.md](../../docs/PROJECT_BLUEPRINT.md) - 总体蓝图
- [FastAPI 官方文档](https://fastapi.tiangolo.com/) - FastAPI 框架文档
- [JWT.io](https://jwt.io/) - JWT 令牌标准