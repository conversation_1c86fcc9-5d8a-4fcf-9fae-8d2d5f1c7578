#!/usr/bin/env python3
"""
Gateway POC Runner
Handles startup, testing, and cleanup of the Gateway POC
"""

import subprocess
import time
import sys
import os
import signal
import asyncio
from pathlib import Path

class GatewayPOCRunner:
    def __init__(self):
        self.compose_process = None
        self.poc_dir = Path(__file__).parent
        
    def setup_environment(self):
        """Setup environment files"""
        env_example = self.poc_dir / ".env.example"
        env_file = self.poc_dir / ".env"
        
        if not env_file.exists() and env_example.exists():
            print("📝 Creating .env file from .env.example...")
            env_file.write_text(env_example.read_text())
            print("✅ .env file created")
        
        # Create acme.json for Traefik SSL
        acme_file = self.poc_dir / "acme.json"
        if not acme_file.exists():
            print("📝 Creating acme.json for Traefik SSL...")
            acme_file.write_text("{}")
            acme_file.chmod(0o600)
            print("✅ acme.json created")
    
    def start_services(self):
        """Start Docker Compose services"""
        print("🚀 Starting Gateway POC services...")
        
        try:
            # Change to POC directory
            os.chdir(self.poc_dir)
            
            # Start services
            self.compose_process = subprocess.Popen(
                ["docker-compose", "up", "--build"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            print("✅ Services starting...")
            print("📊 Traefik Dashboard: http://localhost:8080")
            print("🌐 BFF Gateway: http://localhost")
            print("📝 Logs will be shown below...")
            print("-" * 50)
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to start services: {str(e)}")
            return False
    
    def wait_for_services(self, timeout=60):
        """Wait for services to be ready"""
        print(f"⏳ Waiting for services to be ready (timeout: {timeout}s)...")
        
        import requests
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Check if BFF is responding
                response = requests.get("http://localhost/health", timeout=5)
                if response.status_code == 200:
                    print("✅ Services are ready!")
                    return True
            except:
                pass
            
            time.sleep(2)
            print(".", end="", flush=True)
        
        print(f"\n⚠️  Services didn't become ready within {timeout}s")
        return False
    
    def run_tests(self):
        """Run the test suite"""
        print("\n🧪 Running Gateway POC tests...")
        
        try:
            # Run the test script
            result = subprocess.run(
                [sys.executable, "test_gateway.py"],
                cwd=self.poc_dir,
                capture_output=True,
                text=True
            )
            
            print(result.stdout)
            if result.stderr:
                print("STDERR:", result.stderr)
            
            if result.returncode == 0:
                print("✅ All tests completed")
                return True
            else:
                print(f"❌ Tests failed with return code {result.returncode}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to run tests: {str(e)}")
            return False
    
    def stop_services(self):
        """Stop Docker Compose services"""
        print("\n🛑 Stopping services...")
        
        try:
            if self.compose_process:
                # Send SIGTERM to docker-compose
                self.compose_process.terminate()
                
                # Wait for graceful shutdown
                try:
                    self.compose_process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    # Force kill if needed
                    self.compose_process.kill()
                    self.compose_process.wait()
            
            # Run docker-compose down to clean up
            subprocess.run(
                ["docker-compose", "down", "-v"],
                cwd=self.poc_dir,
                capture_output=True
            )
            
            print("✅ Services stopped")
            
        except Exception as e:
            print(f"⚠️  Error stopping services: {str(e)}")
    
    def show_logs(self):
        """Show service logs in real-time"""
        if self.compose_process:
            try:
                for line in iter(self.compose_process.stdout.readline, ''):
                    if line:
                        print(line.rstrip())
                    if self.compose_process.poll() is not None:
                        break
            except KeyboardInterrupt:
                print("\n⏹️  Log streaming interrupted")
    
    def run_interactive(self):
        """Run POC in interactive mode"""
        print("🎯 Gateway POC - Interactive Mode")
        print("=" * 50)
        
        try:
            self.setup_environment()
            
            if not self.start_services():
                return False
            
            # Wait a bit for services to start
            time.sleep(5)
            
            if not self.wait_for_services():
                print("⚠️  Services may not be fully ready, but continuing...")
            
            print("\n🎉 Gateway POC is running!")
            print("\nAvailable endpoints:")
            print("  • Health Check: http://localhost/health")
            print("  • API Docs: http://localhost/docs")
            print("  • Traefik Dashboard: http://localhost:8080")
            print("\nPress Ctrl+C to stop and run tests, or Ctrl+\\ to quit immediately")
            
            # Show logs
            self.show_logs()
            
        except KeyboardInterrupt:
            print("\n\n🧪 Running tests before shutdown...")
            self.run_tests()
        except Exception as e:
            print(f"\n❌ Error in interactive mode: {str(e)}")
        finally:
            self.stop_services()
    
    def run_test_only(self):
        """Run tests against already running services"""
        print("🧪 Running tests against existing services...")
        
        if not self.wait_for_services(timeout=10):
            print("❌ Services are not ready. Make sure Gateway POC is running.")
            return False
        
        return self.run_tests()


def main():
    runner = GatewayPOCRunner()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "test":
            success = runner.run_test_only()
            sys.exit(0 if success else 1)
        elif command == "start":
            runner.setup_environment()
            if runner.start_services():
                print("Services started. Use 'docker-compose logs -f' to see logs.")
            sys.exit(0)
        elif command == "stop":
            runner.stop_services()
            sys.exit(0)
        else:
            print(f"Unknown command: {command}")
            print("Usage: python run_poc.py [start|stop|test]")
            sys.exit(1)
    else:
        # Interactive mode
        runner.run_interactive()


if __name__ == "__main__":
    main()
