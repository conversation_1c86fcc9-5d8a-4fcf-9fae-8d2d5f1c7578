"""
Embedding service implementation

Provides text embedding functionality using OpenAI-compatible APIs.
"""

import logging
from typing import List, Dict, Any
from openai import AsyncOpenAI
from app.core.config import settings

logger = logging.getLogger(__name__)


class EmbeddingService:
    """Service for text embedding using remote APIs"""
    
    def __init__(self):
        """Initialize the embedding service"""
        self.client = AsyncOpenAI(
            api_key=settings.EMBEDDING_API_KEY,
            base_url=settings.EMBEDDING_BASE_URL
        )
        self.model_name = settings.EMBEDDING_MODEL_NAME
        self.embedding_dim = settings.EMBEDDING_DIM
        
    async def embed_texts(self, texts: List[str]) -> List[List[float]]:
        """
        Embed a list of texts using the remote API.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
            
        Raises:
            RuntimeError: If embedding fails
        """
        try:
            logger.info(f"Embedding {len(texts)} texts using model {self.model_name}")
            
            response = await self.client.embeddings.create(
                model=self.model_name,
                input=texts
            )
            
            # Extract embeddings from response
            embeddings = [item.embedding for item in response.data]
            
            # Validate dimensions
            if embeddings and len(embeddings[0]) != self.embedding_dim:
                logger.warning(
                    f"Embedding dimension mismatch: got {len(embeddings[0])}, "
                    f"expected {self.embedding_dim}"
                )
            
            logger.info(f"Successfully embedded {len(embeddings)} texts")
            return embeddings
            
        except Exception as e:
            logger.error(f"Failed to embed texts: {e}")
            raise RuntimeError(f"Embedding failed: {str(e)}")
    
    async def embed_single_text(self, text: str) -> List[float]:
        """
        Embed a single text.
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector
        """
        embeddings = await self.embed_texts([text])
        return embeddings[0] if embeddings else []
    
    def get_embedding_dimension(self) -> int:
        """Get the embedding dimension"""
        return self.embedding_dim
    
    def get_model_name(self) -> str:
        """Get the model name"""
        return self.model_name
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check if the embedding service is healthy.
        
        Returns:
            Health status information
        """
        try:
            # Test with a simple text
            test_text = "Health check test"
            embedding = await self.embed_single_text(test_text)
            
            return {
                "status": "healthy",
                "model": self.model_name,
                "dimension": len(embedding),
                "expected_dimension": self.embedding_dim,
                "api_accessible": True
            }
            
        except Exception as e:
            logger.error(f"Embedding service health check failed: {e}")
            return {
                "status": "unhealthy",
                "model": self.model_name,
                "error": str(e),
                "api_accessible": False
            }
